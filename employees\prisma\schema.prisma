generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  Id             String           @id @default(uuid()) @db.Uuid
  Name           String?
  Email          String           @unique
  Password       String
  IsActive       Boolean          @default(true)
  IsVerified     Boolean          @default(false)
  RefreshToken   String?
  TokenExpiry    DateTime?
  CreatedAt      DateTime         @default(now())
  UpdatedAt      DateTime         @updatedAt
  LastLoginAt    DateTime?
  Companies      Company[]
  IntegrationLog IntegrationLog[]

  @@map("User")
}

model Company {
  Id                       String                     @id @default(uuid()) @db.Uuid
  Name                     String?
  UserId                   String                     @db.Uuid
  User                     User                       @relation(fields: [UserId], references: [Id])
  XeroAccessToken          String?
  XeroRefreshToken         String?
  XeroTokenExpiry          DateTime?
  XeroRefreshTokenExpiry   DateTime?
  XeroTenantId             String?
  CreatedAt                DateTime                   @default(now())
  UpdatedAt                DateTime                   @updatedAt
  IntegrationLog           IntegrationLog[]
  Employee                 Employee[]
  XeroModuleSync           XeroModuleSync[]

  @@map("Company")
}

model IntegrationLog {
  Id              String     @id @default(uuid()) @db.Uuid
  RequestId       String?    @db.VarChar(255)
  CompanyId       String     @db.Uuid
  Company         Company    @relation(fields: [CompanyId], references: [Id])
  UserId          String?    @db.Uuid
  User            User?      @relation(fields: [UserId], references: [Id])
  ApiName         String?    @db.VarChar(255)
  Method          String?    @db.VarChar(50)
  ApiUrl          String?
  IntegrationName String?    @db.VarChar(255)
  StatusCode      String?    @db.VarChar(10)
  Duration        String?    @db.VarChar(50)
  Message         String?
  Entity          String?    @db.VarChar(255)
  TriggeredBy     String?    @db.VarChar(50)
  SyncStatus      String?    @db.VarChar(50)
  ApiRequest      Json?
  ApiResponse     Json?
  ErrorDetails    Json?
  StartedAt       DateTime?
  CompletedAt     DateTime?
  SyncSummary     Json?
  CreatedAt       DateTime   @default(now())
  UpdatedAt       DateTime   @updatedAt

  @@map("IntegrationLog")
}

model XeroModuleSync {
  Id           String    @id @default(uuid()) @db.Uuid
  CompanyId    String    @db.Uuid
  Company      Company   @relation(fields: [CompanyId], references: [Id])
  ModuleName   String    @db.VarChar(100)
  LastSyncDate DateTime?
  CreatedAt    DateTime  @default(now())
  UpdatedAt    DateTime  @updatedAt

  @@unique([CompanyId, ModuleName])
  @@map("XeroModuleSync")
}

model Employee {
  EmployeeID              String    @id @default(uuid()) @db.Uuid
  Status                  String?   @db.VarChar(50)
  FirstName               String?   @db.VarChar(255)
  LastName                String?   @db.VarChar(255)
  ExternalLinkUrl         String?   @db.VarChar(255)
  ExternalLinkDescription String?
  UpdateUTCDate           DateTime? @db.Date
  CompanyId               String    @db.Uuid
  Company                 Company   @relation(fields: [CompanyId], references: [Id])

  @@map("Employee")
}
