{"name": "xero-employees-sync", "version": "1.0.0", "description": "AWS Lambda function for syncing Xero Employees", "main": "dist/handlers/xeroEmployeesHandler.js", "type": "commonjs", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start-offline": "serverless offline", "deploy": "serverless deploy", "prisma:generate": "npx prisma generate", "prisma:migrate": "npx prisma migrate dev", "prisma:studio": "npx prisma studio", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean && npm run lint && npm run type-check"}, "keywords": ["aws", "lambda", "xero", "employees", "sync", "serverless"], "author": "Your Name", "license": "MIT", "dependencies": {"@aws-sdk/client-sqs": "^3.0.0", "@prisma/client": "^5.0.0", "axios": "^1.6.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.0", "@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "prisma": "^5.0.0", "rimraf": "^5.0.0", "serverless": "^3.0.0", "serverless-offline": "^13.0.0", "serverless-offline-sqs": "^12.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}