// createEmployees.js

require('dotenv').config();
const axios = require('axios');

const ACCESS_TOKEN = process.env.ACCESS_TOKEN;
const TENANT_ID = process.env.TENANT_ID;

const BASE_URL = 'https://api.xero.com/api.xro/2.0/Employees';

async function createEmployee(employee) {
  try {
    const response = await axios.put(
      BASE_URL,
      { employee },
      {
        headers: {
          Authorization: `Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjFDQUY4RTY2NzcyRDZEQzAyOEQ2NzI2RkQwMjYxNTgxNTcwRUZDMTkiLCJ0eXAiOiJKV1QiLCJ4NXQiOiJISy1PWm5jdGJjQW8xbkp2MENZVmdWY09fQmsifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.o1py4ezAJKP0HSnqzv01QSYbJt2trx5-gMDuSRO65GAbzHirUrvNMrsmF93TGBH5ZHnVgYgA_u_g2jLdYv8M23RMWBKqKYxATBhq5Qg1398nb1YGTN4CUNXfTXYMBf_1aglFc0O1y7_2B1A-QKCPj38H1sNKXU8XY94zU1MnuNWuvLW-IadLy_HKsq65_EK96d1pZ9nYM-EW4D-xbrSHyqkgA7NC7HwTXeVxByiLZbGDrtP3fGIejO0rA8H8vS79rSB9fBUgGFBmx6XivAVLt0q96Kpskwzlsd2TSmXjHb8l7iGqy9xscnZ_WDTNOSxF-YZ_C_sbYizcEB1tuXugtA`,
          'Xero-tenant-id': 'cebbe953-9d67-4999-8355-cb194477ddca',
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      }
    );
    console.log(
      `✔️ Created: ${response.data.employee.firstName} ${response.data.employee.lastName}`
    );
  } catch (error) {
    console.error('❌ Error:', error.response?.data.Elements[0] || error.message);
  }
}

async function createEmployees() {
  const employees = Array.from({ length: 10 }, (_, i) => ({
    FirstName: `TestFirst${i + 1}`,
    LastName: `TestLast${i + 1}`,
    Email: `test${i + 1}@example.com`,
    DateOfBirth: '1990-01-01',
    Gender: 'F', // Use 'F' or 'M' or 'U' (unknown)
    StartDate: '2022-01-01',
  }));

  for (const emp of employees) {
    await createEmployee(emp);
  }
}

createEmployees();
