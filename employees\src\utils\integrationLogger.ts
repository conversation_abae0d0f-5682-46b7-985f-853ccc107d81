/**
 * Integration Logger Utility
 *
 * This utility provides comprehensive logging functionality for Lambda function
 * execution and API integration tracking using the IntegrationLog model.
 *
 * Key Features:
 * - Lambda function execution logging (success/failure)
 * - API request/response logging
 * - Error tracking and debugging
 * - Performance monitoring
 * - Integration with Prisma ORM
 *
 * <AUTHOR> Logger Utility
 * @version 1.0.0
 */

import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

// Sync Status Enum
export enum SyncStatus {
  IN_PROGRESS = 'IN_PROGRESS',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  PARTIAL_SUCCESS = 'PARTIAL_SUCCESS',
  CANCELLED = 'CANCELLED',
}

// Production Configuration
const PRODUCTION_CONFIG = {
  MAX_LOG_RETENTION_DAYS: 30,
  MAX_ERROR_MESSAGE_LENGTH: 5000,
  MAX_API_RESPONSE_SIZE: 50000, // 50KB limit for API response logging
  ENABLE_DETAILED_LOGGING: true,
  LOG_PERFORMANCE_METRICS: true,
};

/**
 * Integration Log Data Interface
 */
export interface IntegrationLogData {
  requestId?: string;
  companyId: string;
  apiName: string;
  method?: string;
  apiUrl?: string;
  integrationName?: string;
  statusCode?: string;
  duration?: string;
  message?: string;
  entity?: string;
  triggeredBy?: 'USER' | 'SYSTEM';
  syncStatus?: SyncStatus;
  apiRequest?: any;
  apiResponse?: any;
  errorDetails?: any;
  startedAt?: Date;
  completedAt?: Date;
  syncSummary?: any;
}

/**
 * Lambda Execution Context
 */
export interface LambdaExecutionContext {
  requestId: string;
  companyId: string;
  entity: string;
  triggeredBy: 'USER' | 'SYSTEM';
  startTime: number;
}

/**
 * Performance Metrics Interface
 */
export interface PerformanceMetrics {
  executionTime: number;
  memoryUsed?: number;
  apiCallCount?: number;
  recordsProcessed?: number;
  errorCount?: number;
}

/**
 * Get Prisma client instance with error handling
 */
function getPrismaClient(): PrismaClient {
  try {
    return new PrismaClient({
      log: ['error', 'warn'],
      errorFormat: 'pretty',
    });
  } catch (error) {
    console.error('❌ Failed to initialize Prisma client:', error);
    throw new Error('Database connection failed');
  }
}

/**
 * Safely stringify objects for logging
 */
function safeStringify(obj: any, maxLength: number = PRODUCTION_CONFIG.MAX_API_RESPONSE_SIZE): string {
  try {
    const stringified = JSON.stringify(obj, null, 2);
    return stringified.length > maxLength 
      ? stringified.substring(0, maxLength) + '... [TRUNCATED]'
      : stringified;
  } catch (error) {
    return `[STRINGIFY_ERROR]: ${String(obj)}`;
  }
}

/**
 * Format duration in human-readable format
 */
export function formatDuration(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(2)}s`;
  } else {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = ((milliseconds % 60000) / 1000).toFixed(2);
    return `${minutes}m ${seconds}s`;
  }
}

/**
 * Create integration log entry
 */
export async function createIntegrationLog(logData: IntegrationLogData): Promise<string> {
  const prisma = getPrismaClient();
  
  try {
    const logEntry = await prisma.integrationLog.create({
      data: {
        RequestId: logData.requestId || uuidv4(),
        CompanyId: logData.companyId,
        ApiName: logData.apiName,
        Method: logData.method || 'LAMBDA',
        ApiUrl: logData.apiUrl,
        IntegrationName: logData.integrationName || 'Xero',
        StatusCode: logData.statusCode,
        Duration: logData.duration,
        Message: logData.message,
        Entity: logData.entity,
        TriggeredBy: logData.triggeredBy || 'SYSTEM',
        SyncStatus: logData.syncStatus || SyncStatus.IN_PROGRESS,
        ApiRequest: logData.apiRequest ? JSON.parse(safeStringify(logData.apiRequest)) : null,
        ApiResponse: logData.apiResponse ? JSON.parse(safeStringify(logData.apiResponse)) : null,
        ErrorDetails: logData.errorDetails ? JSON.parse(safeStringify(logData.errorDetails)) : null,
        StartedAt: logData.startedAt || new Date(),
        CompletedAt: logData.completedAt,
        SyncSummary: logData.syncSummary ? JSON.parse(safeStringify(logData.syncSummary)) : null,
      },
    });

    return logEntry.Id;
  } catch (error) {
    console.error('❌ Failed to create integration log:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}
